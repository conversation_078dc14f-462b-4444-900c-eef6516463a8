# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class ClientEntity < Grape::Entity
          expose :id
          include SmartEnergy::Api::V1::Entities::Fields::ClientFields

          # Expose clientable fields directly without nested entities to avoid circular dependency
          expose :clientable do |client|
            case client.clientable_type
            when 'Person'
              {
                id: client.clientable.id,
                first_name: client.clientable.first_name,
                last_name: client.clientable.last_name
              }
            when 'Company'
              {
                id: client.clientable.id,
                company_name: client.clientable.company_name,
                registration_number: client.clientable.registration_number
              }
            end
          end
        end
      end
    end
  end
end
