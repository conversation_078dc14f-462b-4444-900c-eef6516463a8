# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class ClientEntity < Grape::Entity
          include SmartEnergy::Api::V1::Entities::Fields::ClientFields

          expose :id

          # Flatten Person fields when clientable is a Person
          expose :first_name, if: lambda { |client| client.clientable_type == 'Person' } do |client|
            client.clientable.first_name
          end

          expose :last_name, if: lambda { |client| client.clientable_type == 'Person' } do |client|
            client.clientable.last_name
          end

          # Flatten Company fields when clientable is a Company
          expose :company_name, if: lambda { |client| client.clientable_type == 'Company' } do |client|
            client.clientable.company_name
          end

          expose :registration_number, if: lambda { |client| client.clientable_type == 'Company' } do |client|
            client.clientable.registration_number
          end
        end
      end
    end
  end
end
