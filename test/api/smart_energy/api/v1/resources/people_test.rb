# frozen_string_literal: true

require 'test_helper'

module SmartEnergy
  module Api
    module V1
      module Resources
        class PeopleTest < ActionDispatch::IntegrationTest
          setup do
            @tenant = Tenant.create!(name: 'Test Tenant', domain: 'test.example.com')
            Current.tenant = @tenant
          end

          test 'creating a person through API should create both person and client' do
            # Arrange
            person_params = {
              first_name: '<PERSON>',
              last_name: '<PERSON><PERSON>',
              client: {
                tax_code: 'PERSON123',
                phone_number: '555-1234',
                email: '<EMAIL>'
              }
            }

            # Act
            post '/api/v1/people', params: person_params, headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_not_nil json_response['client'], 'Response should include client information'

            # Verify client attributes in response
            assert_equal 'PERSON123', json_response['client']['tax_code']
            assert_equal '555-1234', json_response['client']['phone_number']
            assert_equal '<EMAIL>', json_response['client']['email']

            # Verify in database
            person = Person.find(json_response['id'])
            assert person.client.present?, 'Client should be created in database'
            assert_equal @tenant.id, person.client.tenant_id, 'Client should belong to current tenant'
            assert_equal 'PERSON123', person.client.tax_code
            assert_equal '555-1234', person.client.phone_number
            assert_equal '<EMAIL>', person.client.email
          end

          test 'creating a person without client params should still create a client' do
            # Arrange
            person_params = {
              first_name: 'Jane',
              last_name: 'Smith'
            }

            # Act
            post '/api/v1/people', params: person_params, headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_not_nil json_response['client'], 'Response should include client information'

            # Verify in database
            person = Person.find(json_response['id'])
            assert person.client.present?, 'Client should be created in database'
            assert_equal @tenant.id, person.client.tenant_id, 'Client should belong to current tenant'
          end

          test 'getting all people returns list of people with client information' do
            # Arrange
            person1 = Person.create!(first_name: 'Alice', last_name: 'Johnson')
            person1.client.update!(tax_code: 'ALICE123', email: '<EMAIL>')

            person2 = Person.create!(first_name: 'Bob', last_name: 'Wilson')
            person2.client.update!(tax_code: 'BOB456', email: '<EMAIL>')

            # Act
            get '/api/v1/people', headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_equal 2, json_response.length

            first_person = json_response.find { |p| p['first_name'] == 'Alice' }
            assert_not_nil first_person
            assert_equal 'Johnson', first_person['last_name']
            assert_equal 'ALICE123', first_person['client']['tax_code']
            assert_equal '<EMAIL>', first_person['client']['email']
          end

          test 'getting a specific person returns person with client information' do
            # Arrange
            person = Person.create!(first_name: 'Charlie', last_name: 'Brown')
            person.client.update!(
              tax_code: 'CHARLIE789',
              phone_number: '555-9999',
              email: '<EMAIL>'
            )

            # Act
            get "/api/v1/people/#{person.id}", headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_equal person.id, json_response['id']
            assert_equal 'Charlie', json_response['first_name']
            assert_equal 'Brown', json_response['last_name']
            assert_equal 'CHARLIE789', json_response['client']['tax_code']
            assert_equal '555-9999', json_response['client']['phone_number']
            assert_equal '<EMAIL>', json_response['client']['email']
          end

          test 'getting a non-existent person returns 404' do
            # Act
            get '/api/v1/people/99999', headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :not_found
          end

          teardown do
            Current.tenant = nil
          end
        end
      end
    end
  end
end
