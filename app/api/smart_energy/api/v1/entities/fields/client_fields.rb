# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        module Fields
          module ClientFields
            extend ActiveSupport::Concern

            included do
              expose :tax_code
              expose :phone_number
              expose :email
              expose :clientable_type, as: :client_type
            end
          end
        end
      end
    end
  end
end
