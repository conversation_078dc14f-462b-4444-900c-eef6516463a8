# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class ClientEntity < Grape::Entity
          expose :id
          include SmartEnergy::Api::V1::Entities::Fields::ClientFields

          expose :clientable do |client|
            case client.clientable_type
            when 'Person'
              SmartEnergy::Api::V1::Entities::Fields::PersonFields(client.clientable)
            when 'Company'
              SmartEnergy::Api::V1::Entities::Fields::CompanyFields(client.clientable)
            else
              raise "Unknown clientable type: #{client.clientable_type}"
            end
          end

          expose :clientable do |client|
            entity = Class.new(Grape::Entity) do
              include SmartEnergy::Api::V1::Entities::Fields::PersonFields if client.person?
              include SmartEnergy::Api::V1::Entities::Fields::CompanyFields if client.company?
            end
            entity.represent(client.clientable).serializable_hash
          end
        end
      end
    end
  end
end
