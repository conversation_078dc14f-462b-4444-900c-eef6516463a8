# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class PersonEntity < Grape::Entity
          include SmartEnergy::Api::V1::Entities::Fields::PersonFields

          # Flatten client fields at the first level
          expose :tax_code do |person|
            person.client.tax_code
          end

          expose :phone_number do |person|
            person.client.phone_number
          end

          expose :email do |person|
            person.client.email
          end

          expose :clientable_type, as: :client_type do |person|
            person.client.clientable_type
          end
        end
      end
    end
  end
end
