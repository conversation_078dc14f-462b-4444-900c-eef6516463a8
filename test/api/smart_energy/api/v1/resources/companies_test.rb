# frozen_string_literal: true

require 'test_helper'

module SmartEnergy
  module Api
    module V1
      module Resources
        class CompaniesTest < ActionDispatch::IntegrationTest
          setup do
            @tenant = Tenant.create!(name: 'Test Tenant', domain: 'test.example.com')
            Current.tenant = @tenant
          end

          test 'creating a company without client params should still create a client' do
            # Arrange
            company_params = {
              company_name: 'Acme Inc',
              registration_number: '12345'
            }

            # Act
            post '/api/v1/companies', params: company_params, headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_not_nil json_response['client'], 'Response should include client information'

            # Verify in database
            company = Company.find(json_response['id'])
            assert company.client.present?, 'Client should be created in database'
            assert_equal @tenant.id, company.client.tenant_id, 'Client should belong to current tenant'
          end

          test 'creating a company with client attributes' do
            # Arrange
            company_params = {
              company_name: 'Beta Corp',
              registration_number: '67890',
              client: {
                tax_code: 'CORP123',
                phone_number: '555-6789',
                email: '<EMAIL>'
              }
            }

            # Act
            post '/api/v1/companies', params: company_params, headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)

            # Verify client attributes in response
            assert_equal 'CORP123', json_response['client']['tax_code']
            assert_equal '555-6789', json_response['client']['phone_number']
            assert_equal '<EMAIL>', json_response['client']['email']

            # Verify in database
            company = Company.find(json_response['id'])
            assert_equal 'CORP123', company.client.tax_code
            assert_equal '555-6789', company.client.phone_number
            assert_equal '<EMAIL>', company.client.email
          end

          test 'getting all companies returns list of companies with client information' do
            # Arrange
            company1 = Company.create!(company_name: 'Tech Solutions Inc', registration_number: 'REG001')
            company1.client.update!(tax_code: 'TECH123', email: '<EMAIL>')

            company2 = Company.create!(company_name: 'Global Services LLC', registration_number: 'REG002')
            company2.client.update!(tax_code: 'GLOBAL456', email: '<EMAIL>')

            # Act
            get '/api/v1/companies', headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_equal 2, json_response.length

            tech_company = json_response.find { |c| c['company_name'] == 'Tech Solutions Inc' }
            assert_not_nil tech_company
            assert_equal 'REG001', tech_company['registration_number']
            assert_equal 'TECH123', tech_company['client']['tax_code']
            assert_equal '<EMAIL>', tech_company['client']['email']
          end

          test 'getting a specific company returns company with client information' do
            # Arrange
            company = Company.create!(company_name: 'Innovative Corp', registration_number: 'REG003')
            company.client.update!(
              tax_code: 'INNOV789',
              phone_number: '555-8888',
              email: '<EMAIL>'
            )

            # Act
            get "/api/v1/companies/#{company.id}", headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_equal company.id, json_response['id']
            assert_equal 'Innovative Corp', json_response['company_name']
            assert_equal 'REG003', json_response['registration_number']
            assert_equal 'INNOV789', json_response['client']['tax_code']
            assert_equal '555-8888', json_response['client']['phone_number']
            assert_equal '<EMAIL>', json_response['client']['email']
          end

          test 'getting a non-existent company returns 404' do
            # Act
            get '/api/v1/companies/99999', headers: { 'Origin' => 'http://test.example.com' }

            # Assert
            assert_response :not_found
          end

          teardown do
            Current.tenant = nil
          end
        end
      end
    end
  end
end
