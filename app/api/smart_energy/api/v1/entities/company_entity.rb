# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class CompanyEntity < Grape::Entity
          include SmartEnergy::Api::V1::Entities::Fields::CompanyFields

          # Flatten client fields at the first level
          expose :tax_code do |company|
            company.client.tax_code
          end

          expose :phone_number do |company|
            company.client.phone_number
          end

          expose :email do |company|
            company.client.email
          end

          expose :clientable_type, as: :client_type do |company|
            company.client.clientable_type
          end
        end
      end
    end
  end
end
